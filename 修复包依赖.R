# 修复R包依赖问题的脚本
# 解决dplyr和vctrs包的版本兼容性问题

cat("=== R包依赖修复工具 ===\n\n")

# 1. 检查当前R版本
cat("当前R版本:", R.version.string, "\n")
cat("平台:", R.version$platform, "\n\n")

# 2. 清理并重新安装有问题的包
cat("开始修复包依赖问题...\n\n")

# 卸载有问题的包
packages_to_remove <- c("vctrs", "dplyr", "tidyr", "ggplot2")

cat("1. 卸载可能有问题的包...\n")
for(pkg in packages_to_remove) {
  if(pkg %in% rownames(installed.packages())) {
    cat("   卸载:", pkg, "\n")
    try(remove.packages(pkg), silent = TRUE)
  }
}

# 清理临时文件
cat("\n2. 清理临时文件...\n")
try({
  # 清理临时目录
  temp_files <- list.files(tempdir(), full.names = TRUE)
  unlink(temp_files, recursive = TRUE, force = TRUE)
  cat("   ✓ 临时文件已清理\n")
}, silent = TRUE)

# 重启R会话的提示
cat("\n3. 建议重启R会话...\n")
cat("   请在RStudio中执行: .rs.restartR()\n")
cat("   或者重新启动R程序\n\n")

# 4. 重新安装包（按依赖顺序）
cat("4. 重新安装包（请在重启R后运行）...\n")

install_packages_safely <- function() {
  # 设置CRAN镜像
  options(repos = c(CRAN = "https://cran.rstudio.com/"))
  
  # 按依赖顺序安装
  packages_order <- c(
    "cli",
    "rlang", 
    "vctrs",
    "pillar",
    "tibble",
    "dplyr",
    "tidyr",
    "scales",
    "ggplot2"
  )
  
  cat("开始按顺序安装包...\n")
  
  for(pkg in packages_order) {
    cat("正在安装:", pkg, "...")
    
    try({
      # 强制重新安装
      install.packages(pkg, 
                      dependencies = TRUE,
                      force = TRUE,
                      type = "binary")  # 使用二进制包
      
      # 测试加载
      library(pkg, character.only = TRUE)
      cat(" ✓ 成功\n")
      
    }, silent = FALSE)
  }
  
  cat("\n所有包安装完成！\n")
}

# 5. 创建测试脚本
cat("5. 创建包测试脚本...\n")

test_script <- '
# 测试包加载
cat("测试包加载...\\n")

packages_to_test <- c("dplyr", "ggplot2", "tidyr")

for(pkg in packages_to_test) {
  cat("测试", pkg, "...")
  
  result <- try({
    library(pkg, character.only = TRUE)
    cat(" ✓ 成功\\n")
    TRUE
  }, silent = TRUE)
  
  if(inherits(result, "try-error")) {
    cat(" ✗ 失败\\n")
    cat("错误信息:", as.character(result), "\\n")
  }
}

cat("\\n包测试完成！\\n")
'

writeLines(test_script, "测试包加载.R")
cat("   ✓ 测试脚本已保存: 测试包加载.R\n")

# 6. 提供解决方案
cat("\n=== 解决方案 ===\n")
cat("请按以下步骤操作：\n\n")

cat("步骤1: 重启R会话\n")
cat("   - 在RStudio中: Session -> Restart R\n")
cat("   - 或执行: .rs.restartR()\n\n")

cat("步骤2: 运行安装函数\n")
cat("   source('修复包依赖.R')\n")
cat("   install_packages_safely()\n\n")

cat("步骤3: 测试包加载\n")
cat("   source('测试包加载.R')\n\n")

cat("步骤4: 如果仍有问题，尝试手动安装\n")
cat("   install.packages('vctrs', type='binary', force=TRUE)\n")
cat("   install.packages('dplyr', type='binary', force=TRUE)\n\n")

# 7. 备用方案：使用基础R的简化版本
cat("=== 备用方案：创建无依赖版本 ===\n")

backup_script <- '
# 学生成绩分析 - 基础R版本（无外部依赖）
# 当包依赖有问题时使用此版本

cat("=== 学生成绩分析（基础R版本）===\\n")

# 读取数据
if(file.exists("1.csv")) {
  data <- read.csv("1.csv", header = TRUE, stringsAsFactors = FALSE)
  cat("数据读取成功，维度:", dim(data), "\\n")
  
  # 基本统计
  target_col <- which(names(data) == "MidExamScore")
  if(length(target_col) > 0) {
    mid_scores <- data[, target_col]
    cat("中考成绩统计:\\n")
    cat("  均值:", round(mean(mid_scores, na.rm = TRUE), 2), "\\n")
    cat("  标准差:", round(sd(mid_scores, na.rm = TRUE), 2), "\\n")
    cat("  范围:", round(range(mid_scores, na.rm = TRUE), 2), "\\n")
  }
  
  # 简单回归分析
  predictor_cols <- grep("PreExamScore", names(data))
  if(length(predictor_cols) >= 1 && length(target_col) > 0) {
    # 使用第一个预考成绩做简单回归
    x <- data[, predictor_cols[1]]
    y <- data[, target_col]
    
    # 移除缺失值
    complete_cases <- complete.cases(x, y)
    x <- x[complete_cases]
    y <- y[complete_cases]
    
    if(length(x) > 10) {
      model <- lm(y ~ x)
      cat("\\n简单线性回归结果:\\n")
      print(summary(model))
      
      # 基础图形
      png("简单散点图.png", width = 800, height = 600)
      plot(x, y, main = "预考成绩 vs 中考成绩", 
           xlab = "预考成绩", ylab = "中考成绩",
           pch = 16, col = "steelblue")
      abline(model, col = "red", lwd = 2)
      dev.off()
      cat("散点图已保存: 简单散点图.png\\n")
    }
  }
  
} else {
  cat("错误：找不到数据文件 1.csv\\n")
}

cat("\\n基础分析完成！\\n")
'

writeLines(backup_script, "基础版本分析.R")
cat("   ✓ 备用脚本已保存: 基础版本分析.R\n")

cat("\n如果包问题无法解决，可以运行备用版本：\n")
cat("   source('基础版本分析.R')\n")

cat("\n=== 修复工具准备完成 ===\n")
