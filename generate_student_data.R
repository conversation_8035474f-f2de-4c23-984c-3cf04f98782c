# =============================================================================
# 生成1000名学生数据的R脚本
# 确保数据质量和模型拟合效果
# =============================================================================

cat("=== 开始生成1000名学生数据 ===\n")
cat("开始时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n")

# 设置随机种子以确保结果可重复
set.seed(42)

# 1. 数据生成参数设置
# =============================================================================
n_students <- 900   # 学生数量（减少到900）
n_exams <- 50      # 预考次数

cat("## 1. 数据生成参数\n")
cat("学生数量:", n_students, "\n")
cat("预考次数:", n_exams, "\n\n")

# 2. 生成基础学生能力分布（降低质量）
# =============================================================================
cat("## 2. 生成学生基础能力分布（更真实的分布）\n")

# 创建混合分布：优秀学生、普通学生、困难学生
set.seed(123)  # 改变随机种子
excellent_students <- round(n_students * 0.15)  # 15%优秀学生
normal_students <- round(n_students * 0.70)     # 70%普通学生
struggling_students <- n_students - excellent_students - normal_students  # 15%困难学生

student_ability <- c(
  rnorm(excellent_students, mean = 95, sd = 8),   # 优秀学生：高分但变异小
  rnorm(normal_students, mean = 75, sd = 18),     # 普通学生：中等分数，变异大
  rnorm(struggling_students, mean = 45, sd = 12)  # 困难学生：低分
)

# 随机打乱顺序
student_ability <- sample(student_ability)
# 限制在合理范围内 (0-120)
student_ability <- pmax(0, pmin(120, student_ability))

cat("学生能力分布统计:\n")
cat("均值:", round(mean(student_ability), 2), "\n")
cat("标准差:", round(sd(student_ability), 2), "\n")
cat("范围:", round(range(student_ability), 2), "\n\n")

# 3. 生成预考成绩
# =============================================================================
cat("## 3. 生成50次预考成绩\n")

# 创建数据框
student_data <- data.frame(matrix(nrow = n_students, ncol = n_exams + 1))
colnames(student_data) <- c(paste0("PreExamScore", 1:n_exams), "MidExamScore")

# 为每次考试生成成绩（增加更多噪声和不一致性）
for (exam_i in 1:n_exams) {
  cat("生成第", exam_i, "次预考成绩...\r")

  # 考试难度系数 (更大的变化范围)
  exam_difficulty <- rnorm(1, mean = 0, sd = 12)

  # 学生状态波动（模拟考试当天状态）
  daily_condition <- rnorm(n_students, mean = 0, sd = 6)

  # 基于学生能力 + 考试难度 + 状态波动 + 更大的随机噪声
  base_scores <- student_ability + exam_difficulty + daily_condition

  # 增加更大的随机噪声
  noise <- rnorm(n_students, mean = 0, sd = 12)
  scores <- base_scores + noise

  # 添加一些异常值（约5%的学生可能有异常表现）
  outlier_indices <- sample(n_students, round(n_students * 0.05))
  outlier_effects <- sample(c(-25, -15, 15, 25), length(outlier_indices), replace = TRUE)
  scores[outlier_indices] <- scores[outlier_indices] + outlier_effects

  # 限制在合理范围内 (0-120)
  scores <- pmax(0, pmin(120, round(scores)))

  student_data[, exam_i] <- scores
}

cat("\n预考成绩生成完成！\n\n")

# 4. 生成中考成绩 (目标变量) - 降低预测性
# =============================================================================
cat("## 4. 生成中考成绩 (目标变量) - 更复杂的关系\n")

# 计算每个学生的平均预考成绩
avg_pre_scores <- rowMeans(student_data[, 1:n_exams], na.rm = TRUE)

# 中考成绩基于更复杂的因素（降低线性关系强度）:
# - 学生基础能力 (30%) - 降低
# - 平均预考成绩 (40%) - 降低
# - 最近5次考试表现 (15%) - 新增
# - 随机因素/压力等 (15%) - 增加

# 计算最近5次考试的平均成绩（模拟临近中考的表现更重要）
recent_scores <- rowMeans(student_data[, (n_exams-4):n_exams], na.rm = TRUE)

# 生成中考压力因子（有些学生考试发挥失常）
exam_stress <- rnorm(n_students, mean = 0, sd = 8)
# 约10%的学生有严重的考试焦虑
stress_indices <- sample(n_students, round(n_students * 0.1))
exam_stress[stress_indices] <- exam_stress[stress_indices] - 15

mid_exam_scores <- 0.30 * student_ability +
                   0.40 * avg_pre_scores +
                   0.15 * recent_scores +
                   0.15 * exam_stress

# 添加一些非线性效应（优秀学生可能过度自信，困难学生可能超常发挥）
for (i in 1:n_students) {
  if (student_ability[i] > 90 && runif(1) < 0.1) {
    mid_exam_scores[i] <- mid_exam_scores[i] - 10  # 优秀学生偶尔失误
  }
  if (student_ability[i] < 50 && runif(1) < 0.05) {
    mid_exam_scores[i] <- mid_exam_scores[i] + 15  # 困难学生偶尔超常发挥
  }
}

# 限制在合理范围内并四舍五入
mid_exam_scores <- pmax(0, pmin(120, round(mid_exam_scores)))

student_data$MidExamScore <- mid_exam_scores

cat("中考成绩统计:\n")
cat("均值:", round(mean(mid_exam_scores), 2), "\n")
cat("标准差:", round(sd(mid_exam_scores), 2), "\n")
cat("范围:", round(range(mid_exam_scores), 2), "\n\n")

# 5. 添加更复杂的缺失值模式
# =============================================================================
cat("## 5. 添加更复杂的缺失值模式\n")

# 为预考成绩添加更多缺失值 (约4-6%)
base_missing_rate <- 0.05  # 基础5%缺失率

for (exam_i in 1:n_exams) {
  # 某些考试的缺失率更高（模拟学生请假、生病等）
  if (exam_i %% 10 == 0) {  # 每10次考试中有一次缺失率更高
    current_missing_rate <- base_missing_rate * 1.5
  } else {
    current_missing_rate <- base_missing_rate
  }

  n_missing <- rbinom(1, n_students, current_missing_rate)
  if (n_missing > 0) {
    missing_indices <- sample(n_students, n_missing)
    student_data[missing_indices, exam_i] <- NA
  }
}

# 某些学生更容易缺考（模拟经常请假的学生）
frequent_absent_students <- sample(n_students, round(n_students * 0.05))  # 5%的学生
for (student_id in frequent_absent_students) {
  # 这些学生额外缺失一些考试
  additional_missing <- sample(1:n_exams, sample(3:8, 1))  # 额外缺失3-8次考试
  student_data[student_id, additional_missing] <- NA
}

# 为中考成绩添加缺失值 (约2%)
n_missing_mid <- rbinom(1, n_students, 0.02)
if (n_missing_mid > 0) {
  missing_indices <- sample(n_students, n_missing_mid)
  student_data[missing_indices, "MidExamScore"] <- NA
}

# 统计缺失值
total_missing <- sum(is.na(student_data))
missing_per_col <- colSums(is.na(student_data))

cat("总缺失值数量:", total_missing, "\n")
cat("缺失值比例:", round(total_missing / (n_students * (n_exams + 1)) * 100, 2), "%\n")
cat("中考成绩缺失数量:", missing_per_col["MidExamScore"], "\n\n")

# 6. 数据质量检查
# =============================================================================
cat("## 6. 数据质量检查\n")

# 检查数据范围
cat("预考成绩范围检查:\n")
pre_exam_cols <- paste0("PreExamScore", 1:n_exams)
for (i in 1:min(5, n_exams)) {  # 检查前5列
  col_range <- range(student_data[, pre_exam_cols[i]], na.rm = TRUE)
  cat(sprintf("%s: [%.0f, %.0f]\n", pre_exam_cols[i], col_range[1], col_range[2]))
}

cat("\n中考成绩范围:", range(student_data$MidExamScore, na.rm = TRUE), "\n")

# 检查相关性
cat("\n相关性检查 (前5次预考与中考):\n")
for (i in 1:5) {
  cor_val <- cor(student_data[, i], student_data$MidExamScore, use = "complete.obs")
  cat(sprintf("%s与中考相关性: %.3f\n", pre_exam_cols[i], cor_val))
}

# 7. 保存数据
# =============================================================================
cat("\n## 7. 保存数据\n")

# 备份原始数据
if (file.exists("1.csv")) {
  file.copy("1.csv", "1_backup.csv", overwrite = TRUE)
  cat("✓ 原始数据已备份为: 1_backup.csv\n")
}

# 保存新数据
write.csv(student_data, "1.csv", row.names = FALSE, na = "")
cat("✓ 新数据已保存为: 1.csv\n")

# 保存额外副本
write.csv(student_data, "student_data_1000.csv", row.names = FALSE, na = "")
cat("✓ 数据副本已保存为: student_data_1000.csv\n")

# 8. 生成数据报告
# =============================================================================
cat("\n## 8. 数据生成报告\n")
cat("=== 数据生成完成 ===\n")
cat("✓ 生成学生数量:", nrow(student_data), "\n")
cat("✓ 变量数量:", ncol(student_data), "\n")
cat("✓ 预考成绩列:", n_exams, "\n")
cat("✓ 目标变量: MidExamScore\n")
cat("✓ 数据完整性:", round((1 - total_missing / (n_students * (n_exams + 1))) * 100, 1), "%\n")

cat("\n数据特征:\n")
cat("- 学生能力呈混合分布（优秀/普通/困难学生）\n")
cat("- 预考成绩包含更多噪声和异常值\n")
cat("- 中考成绩关系更复杂，预测性降低\n")
cat("- 包含更复杂的缺失值模式\n")
cat("- 数据质量适中，更接近真实情况\n")
cat("- 所有成绩在0-120分范围内\n")

cat("\n完成时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
cat("数据已准备就绪，可以运行回归分析！\n")
