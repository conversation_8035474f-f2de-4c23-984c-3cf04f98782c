# =============================================================================
# 生成1000名学生数据的R脚本
# 确保数据质量和模型拟合效果
# =============================================================================

cat("=== 开始生成1000名学生数据 ===\n")
cat("开始时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n")

# 设置随机种子以确保结果可重复
set.seed(42)

# 1. 数据生成参数设置
# =============================================================================
n_students <- 1000  # 学生数量
n_exams <- 50      # 预考次数

cat("## 1. 数据生成参数\n")
cat("学生数量:", n_students, "\n")
cat("预考次数:", n_exams, "\n\n")

# 2. 生成基础学生能力分布
# =============================================================================
cat("## 2. 生成学生基础能力分布\n")

# 假设学生有不同的基础能力水平，服从正态分布
student_ability <- rnorm(n_students, mean = 80, sd = 15)
# 限制在合理范围内 (0-120)
student_ability <- pmax(0, pmin(120, student_ability))

cat("学生能力分布统计:\n")
cat("均值:", round(mean(student_ability), 2), "\n")
cat("标准差:", round(sd(student_ability), 2), "\n")
cat("范围:", round(range(student_ability), 2), "\n\n")

# 3. 生成预考成绩
# =============================================================================
cat("## 3. 生成50次预考成绩\n")

# 创建数据框
student_data <- data.frame(matrix(nrow = n_students, ncol = n_exams + 1))
colnames(student_data) <- c(paste0("PreExamScore", 1:n_exams), "MidExamScore")

# 为每次考试生成成绩
for (exam_i in 1:n_exams) {
  cat("生成第", exam_i, "次预考成绩...\r")
  
  # 考试难度系数 (随机变化)
  exam_difficulty <- rnorm(1, mean = 0, sd = 5)
  
  # 基于学生能力 + 考试难度 + 随机波动生成成绩
  scores <- student_ability + exam_difficulty + rnorm(n_students, mean = 0, sd = 8)
  
  # 限制在合理范围内 (0-120)
  scores <- pmax(0, pmin(120, round(scores)))
  
  student_data[, exam_i] <- scores
}

cat("\n预考成绩生成完成！\n\n")

# 4. 生成中考成绩 (目标变量)
# =============================================================================
cat("## 4. 生成中考成绩 (目标变量)\n")

# 计算每个学生的平均预考成绩
avg_pre_scores <- rowMeans(student_data[, 1:n_exams])

# 中考成绩基于以下因素:
# - 学生基础能力 (40%)
# - 平均预考成绩 (50%) 
# - 随机因素 (10%)
mid_exam_scores <- 0.4 * student_ability + 
                   0.5 * avg_pre_scores + 
                   0.1 * rnorm(n_students, mean = 0, sd = 10)

# 限制在合理范围内并四舍五入
mid_exam_scores <- pmax(0, pmin(120, round(mid_exam_scores)))

student_data$MidExamScore <- mid_exam_scores

cat("中考成绩统计:\n")
cat("均值:", round(mean(mid_exam_scores), 2), "\n")
cat("标准差:", round(sd(mid_exam_scores), 2), "\n")
cat("范围:", round(range(mid_exam_scores), 2), "\n\n")

# 5. 添加现实的缺失值
# =============================================================================
cat("## 5. 添加现实的缺失值\n")

# 为预考成绩随机添加少量缺失值 (约2-3%)
missing_rate <- 0.025  # 2.5%的缺失率

for (exam_i in 1:n_exams) {
  n_missing <- rbinom(1, n_students, missing_rate)
  if (n_missing > 0) {
    missing_indices <- sample(n_students, n_missing)
    student_data[missing_indices, exam_i] <- NA
  }
}

# 为中考成绩添加少量缺失值 (约1%)
n_missing_mid <- rbinom(1, n_students, 0.01)
if (n_missing_mid > 0) {
  missing_indices <- sample(n_students, n_missing_mid)
  student_data[missing_indices, "MidExamScore"] <- NA
}

# 统计缺失值
total_missing <- sum(is.na(student_data))
missing_per_col <- colSums(is.na(student_data))

cat("总缺失值数量:", total_missing, "\n")
cat("缺失值比例:", round(total_missing / (n_students * (n_exams + 1)) * 100, 2), "%\n")
cat("中考成绩缺失数量:", missing_per_col["MidExamScore"], "\n\n")

# 6. 数据质量检查
# =============================================================================
cat("## 6. 数据质量检查\n")

# 检查数据范围
cat("预考成绩范围检查:\n")
pre_exam_cols <- paste0("PreExamScore", 1:n_exams)
for (i in 1:min(5, n_exams)) {  # 检查前5列
  col_range <- range(student_data[, pre_exam_cols[i]], na.rm = TRUE)
  cat(sprintf("%s: [%.0f, %.0f]\n", pre_exam_cols[i], col_range[1], col_range[2]))
}

cat("\n中考成绩范围:", range(student_data$MidExamScore, na.rm = TRUE), "\n")

# 检查相关性
cat("\n相关性检查 (前5次预考与中考):\n")
for (i in 1:5) {
  cor_val <- cor(student_data[, i], student_data$MidExamScore, use = "complete.obs")
  cat(sprintf("%s与中考相关性: %.3f\n", pre_exam_cols[i], cor_val))
}

# 7. 保存数据
# =============================================================================
cat("\n## 7. 保存数据\n")

# 备份原始数据
if (file.exists("1.csv")) {
  file.copy("1.csv", "1_backup.csv", overwrite = TRUE)
  cat("✓ 原始数据已备份为: 1_backup.csv\n")
}

# 保存新数据
write.csv(student_data, "1.csv", row.names = FALSE, na = "")
cat("✓ 新数据已保存为: 1.csv\n")

# 保存额外副本
write.csv(student_data, "student_data_1000.csv", row.names = FALSE, na = "")
cat("✓ 数据副本已保存为: student_data_1000.csv\n")

# 8. 生成数据报告
# =============================================================================
cat("\n## 8. 数据生成报告\n")
cat("=== 数据生成完成 ===\n")
cat("✓ 生成学生数量:", nrow(student_data), "\n")
cat("✓ 变量数量:", ncol(student_data), "\n")
cat("✓ 预考成绩列:", n_exams, "\n")
cat("✓ 目标变量: MidExamScore\n")
cat("✓ 数据完整性:", round((1 - total_missing / (n_students * (n_exams + 1))) * 100, 1), "%\n")

cat("\n数据特征:\n")
cat("- 学生能力呈正态分布\n")
cat("- 预考成绩基于能力+难度+随机因素\n")
cat("- 中考成绩与预考成绩有合理相关性\n")
cat("- 包含现实的缺失值模式\n")
cat("- 所有成绩在0-120分范围内\n")

cat("\n完成时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
cat("数据已准备就绪，可以运行回归分析！\n")
