# 修复R包问题的脚本
cat("开始修复R包问题...\n")

# 1. 设置用户库路径
user_lib <- file.path(Sys.getenv("R_USER"), "library")
if (!dir.exists(user_lib)) {
  dir.create(user_lib, recursive = TRUE)
  cat("创建用户库目录:", user_lib, "\n")
}
.libPaths(c(user_lib, .libPaths()))
cat("当前库路径:\n")
print(.libPaths())

# 2. 移除有问题的包
cat("移除有问题的包...\n")
tryCatch({
  remove.packages("vctrs", lib = user_lib)
  cat("已移除vctrs包\n")
}, error = function(e) {
  cat("vctrs包移除失败或不存在:", e$message, "\n")
})

tryCatch({
  remove.packages("dplyr", lib = user_lib)
  cat("已移除dplyr包\n")
}, error = function(e) {
  cat("dplyr包移除失败或不存在:", e$message, "\n")
})

# 3. 重新安装包到用户库
cat("重新安装包到用户库...\n")
install.packages("vctrs", lib = user_lib, dependencies = TRUE, repos = "https://cran.rstudio.com/")
install.packages("dplyr", lib = user_lib, dependencies = TRUE, repos = "https://cran.rstudio.com/")

# 4. 测试包加载
cat("测试包加载...\n")
tryCatch({
  library(dplyr)
  cat("✓ dplyr包加载成功！\n")
}, error = function(e) {
  cat("✗ dplyr包加载失败:", e$message, "\n")
})

cat("包修复脚本完成！\n")
