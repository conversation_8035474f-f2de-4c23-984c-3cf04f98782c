# 修复R包问题的脚本
cat("开始修复R包问题...\n")

# 1. 移除有问题的包
cat("移除有问题的包...\n")
tryCatch({
  remove.packages("vctrs")
  cat("已移除vctrs包\n")
}, error = function(e) {
  cat("vctrs包移除失败或不存在:", e$message, "\n")
})

tryCatch({
  remove.packages("dplyr")
  cat("已移除dplyr包\n")
}, error = function(e) {
  cat("dplyr包移除失败或不存在:", e$message, "\n")
})

# 2. 清理包缓存
cat("清理包缓存...\n")
.libPaths()

# 3. 重新安装包
cat("重新安装包...\n")
install.packages("vctrs", dependencies = TRUE, repos = "https://cran.rstudio.com/")
install.packages("dplyr", dependencies = TRUE, repos = "https://cran.rstudio.com/")

# 4. 测试包加载
cat("测试包加载...\n")
tryCatch({
  library(dplyr)
  cat("✓ dplyr包加载成功！\n")
}, error = function(e) {
  cat("✗ dplyr包加载失败:", e$message, "\n")
})

cat("包修复脚本完成！\n")
